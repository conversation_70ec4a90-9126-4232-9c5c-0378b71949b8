import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:sasthra/core/services/token_service.dart';
import 'package:sasthra/core/services/api_service.dart';

class CenterInfo extends StatefulWidget {
  const CenterInfo({Key? key}) : super(key: key);

  @override
  _CenterInfoState createState() => _CenterInfoState();
}

class _CenterInfoState extends State<CenterInfo> {
  Map<String, dynamic>? center;
  bool isLoading = true;
  bool isError = false;
  String errorMessage = '';

  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    fetchCenterDetails();
  }

  Future<void> refreshCenterData() async {
    await fetchCenterDetails();
  }

  Future<void> fetchCenterDetails() async {
    setState(() {
      isLoading = true;
      isError = false;
    });

    try {
      final currentToken = await _tokenService.getToken();

      if (currentToken == null || !(await _tokenService.isTokenValid())) {
        setState(() {
          isError = true;
          errorMessage = 'Authentication token has expired. Please log in again.';
          isLoading = false;
        });
        return;
      }

      final data = await _apiService.fetchParentDashboard();
      final centerData = data['center'];

      setState(() {
        center = centerData;
        isLoading = false;
      });
    } catch (e) {
      String msg = 'Unable to fetch center details. Please try again.';
      if (e.toString().contains('401')) {
        msg = 'Session expired. Please log in again.';
      } else if (e.toString().contains('Network')) {
        msg = 'Network error. Please check your connection.';
      }
      setState(() {
        isError = true;
        errorMessage = msg;
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
            stops: [0.0, 1.0],
          ),
        ),
        child: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (isLoading) {
      return Center(
        child: _buildGlassContainer(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: const [
              SizedBox(
                width: 48,
                height: 48,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              SizedBox(height: 16),
              Text(
                'Loading center information...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (isError) {
      return Center(
        child: _buildGlassContainer(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("⚠️", style: TextStyle(fontSize: 40)),
              const SizedBox(height: 12),
              const Text(
                'Error Loading Center Information',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  decoration: TextDecoration.none,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                errorMessage,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  decoration: TextDecoration.none,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: refreshCenterData,
                icon: const Icon(Icons.refresh, color: Colors.white),
                label: const Text(
                  'Retry',
                  style: TextStyle(
                    color: Colors.white,
                    decoration: TextDecoration.none,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.2),
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: const BorderSide(color: Colors.white30),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (center == null) {
      return Center(
        child: _buildGlassContainer(
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("🏫", style: TextStyle(fontSize: 40)),
              SizedBox(height: 12),
              Text(
                "No center information available",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: refreshCenterData,
      color: Colors.white,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildGlassContainer(
              child: Column(
                children: [
                  _buildProfileHeader(),
                  const SizedBox(height: 20),
                  _buildCenterInformation(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // --- Helper Widgets ---

  Widget _buildGlassContainer({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            padding: const EdgeInsets.all(24),
            child: child,
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Column(
      children: [
        const CircleAvatar(
          radius: 35,
          backgroundColor: Colors.white54,
          child: Icon(Icons.account_balance, size: 40, color: Colors.white),
        ),
        const SizedBox(height: 10),
        const Text(
          "Center Profile",
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            decoration: TextDecoration.none,
          ),
        ),
        const SizedBox(height: 10),
        Container(
          height: 2,
          width: 60,
          color: Colors.white,
        ),
      ],
    );
  }

  Widget _buildCenterInformation() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Center Information"),
        _buildInfoItem("Center Name", center!['name'] ?? "N/A"),
        _buildInfoItem("Center Code", center!['center_code'] ?? "N/A"),
        _buildInfoItem("Email Address", center!['email'] ?? "N/A"),
        _buildInfoItem("Phone Number", center!['phone']?.toString() ?? "N/A"),
        const SizedBox(height: 20),
        _buildQuoteCard(),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            decoration: TextDecoration.none,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 1,
          width: 150,
          color: Colors.white,
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildQuoteCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: const Text(
        "\"Empowering education through excellence...\"",
        style: TextStyle(
          fontStyle: FontStyle.italic,
          color: Colors.white,
          fontSize: 15,
          decoration: TextDecoration.none,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.only(top: 5, right: 8),
            decoration: const BoxDecoration(
              color: Colors.white70,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                    decoration: TextDecoration.none,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.none,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}