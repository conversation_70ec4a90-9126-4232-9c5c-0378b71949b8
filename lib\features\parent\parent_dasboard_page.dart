import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:ui';
import 'package:go_router/go_router.dart';
import '../../core/services/token_service.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';
import '../../core/theme/app_theme.dart';

// Parent Data Model
class Parent {
  final String username;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String occupation;
  final String relationship;
  final String createdAt;
  final double? annualIncome;
  final String studentId;
  final bool isActive;
  final Map<String, dynamic>? student; // Include student data from API

  Parent({
    required this.username,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.occupation,
    required this.relationship,
    required this.createdAt,
    this.annualIncome,
    required this.studentId,
    required this.isActive,
    this.student,
  });

  factory Parent.fromJson(Map<String, dynamic> json) {
    return Parent(
      username: json['username'] ?? 'Unknown',
      firstName: json['first_name'] ?? 'Unknown',
      lastName: json['last_name'] ?? 'Unknown',
      email: json['parent_email'] ?? 'Unknown',
      phone: json['phone'] ?? 'Unknown',
      occupation: json['occupation'] ?? 'Unknown',
      relationship: json['relationship'] ?? 'Unknown',
      createdAt: json['created_at'] ?? 'Unknown',
      annualIncome: json['annual_income_inr']?.toDouble(),
      studentId: json['student_id'] ?? 'Unknown',
      isActive: json['is_active'] ?? false,
      student: json['student'],
    );
  }
}

// Parent Provider for state management
class ParentProvider with ChangeNotifier {
  Parent? _parent;
  bool _isLoading = true;
  bool _isError = false;
  String _errorMessage = '';

  Parent? get parent => _parent;
  bool get isLoading => _isLoading;
  bool get isError => _isError;
  String get errorMessage => _errorMessage;

  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  Future<void> fetchParentData() async {
    _setLoading(true);
    _setError(false, '');

    try {
      AppLogger.info('Fetching parent dashboard data');
      final currentToken = await _tokenService.getToken();

      if (currentToken == null || !(await _tokenService.isTokenValid())) {
        AppLogger.warning('Token validation failed');
        _setError(true, 'Authentication token has expired. Please log in again.');
        return;
      }

      final data = await _apiService.fetchParentDashboard();
      final parentData = Parent.fromJson(data['parent'] ?? data);

      _parent = parentData;
      _setLoading(false);
      AppLogger.info('Parent dashboard data loaded successfully');
    } catch (e) {
      AppLogger.error('Failed to fetch parent data: $e');
      String msg = 'Unable to fetch parent details. Please try again.';
      if (e.toString().contains('401')) {
        msg = 'Session expired. Please log in again.';
      } else if (e.toString().contains('Network')) {
        msg = 'Network error. Please check your connection.';
      }
      _setError(true, msg);
      _setLoading(false);
    }
  }

  Future<void> refreshData() async {
    await fetchParentData();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(bool error, String message) {
    _isError = error;
    _errorMessage = message;
    notifyListeners();
  }
}

// Parent Dashboard Page - Main widget for parent role dashboard

class ParentDashboard extends StatefulWidget {
  @override
  _ParentDashboardState createState() => _ParentDashboardState();
}

class _ParentDashboardState extends State<ParentDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isInitialLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Initial loading handled by provider
    Future.delayed(Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isInitialLoading = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
            stops: [0.0, 1.0],
          ),
        ),
        child: Consumer<ParentProvider>(
          builder: (context, provider, child) {
            if (_isInitialLoading) {
              return _buildLoadingView();
            }

            if (provider.isError) {
              return _buildErrorView(provider);
            }

            final parent = provider.parent ?? Parent(
              username: 'Unknown',
              firstName: 'Unknown',
              lastName: 'Unknown',
              email: 'Unknown',
              phone: 'Unknown',
              occupation: 'Unknown',
              relationship: 'Unknown',
              createdAt: 'Unknown',
              studentId: 'Unknown',
              isActive: false,
              student: null,
            );

            return CustomScrollView(
              slivers: [
                // Hero Section SliverAppBar
                SliverAppBar(
                  expandedHeight: 220.0,
                  floating: false,
                  pinned: true,
                  backgroundColor: Colors.transparent,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        // Gradient Background (already handled by container)
                        Container(),
                        // Floating decorative elements
                        Positioned(
                          top: 40,
                          left: 20,
                          child: AnimatedContainer(
                            duration: Duration(seconds: 4),
                            curve: Curves.easeInOut,
                            transform: Matrix4.translationValues(0, -10, 0),
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white.withOpacity(0.1),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 40,
                          right: 20,
                          child: AnimatedContainer(
                            duration: Duration(seconds: 4),
                            curve: Curves.easeInOut,
                            transform: Matrix4.translationValues(0, 10, 0),
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white.withOpacity(0.1),
                              ),
                            ),
                          ),
                        ),
                        // Profile Content
                        Padding(
                          padding: EdgeInsets.all(24.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              _buildGlassContainer(
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 30,
                                      backgroundColor: Colors.white.withOpacity(0.2),
                                      child: Icon(Icons.person, color: Colors.white, size: 30),
                                    ),
                                    SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Welcome back, ${parent.firstName}!',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 24,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            'Here\'s everything you need to know about your child\'s progress and account details.',
                                            style: TextStyle(
                                              color: Colors.white70,
                                              fontSize: 14,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Tabs Sliver
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _SliverAppBarDelegate(
                    _buildTabBar(),
                  ),
                ),
                // Content
                SliverFillRemaining(
                  child: provider.isLoading
                      ? _buildLoadingSkeleton()
                      : TabBarView(
                          controller: _tabController,
                          children: [
                            _buildOverviewTab(parent),
                            _buildFinancialTab(parent),
                          ],
                        ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white70,
      indicator: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        color: Colors.white.withOpacity(0.2),
      ),
      labelStyle: const TextStyle(fontWeight: FontWeight.w600),
      unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.w500),
      tabs: const [
        Tab(
          icon: Icon(Icons.dashboard_outlined, size: 20),
          text: 'Overview',
        ),
        Tab(
          icon: Icon(Icons.attach_money_outlined, size: 20),
          text: 'Financial',
        ),
      ],
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: _buildGlassContainer(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 48,
              height: 48,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Loading dashboard...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(ParentProvider provider) {
    return Center(
      child: _buildGlassContainer(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.white70),
            SizedBox(height: 16),
            Text(
              'Error Loading Dashboard',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 18,
              ),
            ),
            SizedBox(height: 8),
            Text(
              provider.errorMessage,
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: provider.refreshData,
              icon: Icon(Icons.refresh, color: Colors.white),
              label: Text(
                'Retry',
                style: TextStyle(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white.withOpacity(0.2),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.white.withOpacity(0.3)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGlassContainer({required Widget child}) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            padding: const EdgeInsets.all(20),
            child: child,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingSkeleton() {
    return RefreshIndicator(
      onRefresh: () => Provider.of<ParentProvider>(context, listen: false).refreshData(),
      color: Colors.white,
      child: SingleChildScrollView(
        physics: AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            GridView.count(
              crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 2.5,
              children: List.generate(4, (index) => _buildSkeletonCard()),
            ),
            SizedBox(height: 16),
            _buildGlassContainer(
              child: Column(
                children: List.generate(
                  5,
                  (index) => Container(
                    height: 60,
                    margin: EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          margin: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        Expanded(
                          child: Container(
                            height: 20,
                            margin: EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkeletonCard() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 1500),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          SizedBox(height: 8),
          Container(
            height: 12,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(Parent parent) {
    final studentData = parent.student;
    final studentName = '${studentData?['first_name'] ?? ''} ${studentData?['last_name'] ?? ''}';
    final studentId = studentData?['id']?.toString() ?? 'N/A';

    return RefreshIndicator(
      onRefresh: () => Provider.of<ParentProvider>(context, listen: false).refreshData(),
      color: Colors.white,
      child: SingleChildScrollView(
        physics: AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            // Stats Grid
            GridView.count(
              crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 2,
              children: [
                _buildStatCard(
                  icon: Icons.person,
                  label: 'Parent Name',
                  value: '${parent.firstName} ${parent.lastName}',
                  color: Colors.blue,
                ),
                _buildStatCard(
                  icon: Icons.phone,
                  label: 'Contact',
                  value: parent.phone,
                  color: Colors.green,
                ),
                _buildStatCard(
                  icon: Icons.email,
                  label: 'Email',
                  value: parent.email,
                  color: Colors.purple,
                ),
                
              ],
            ),
            SizedBox(height: 16),
            // Parent Details Section
            _buildGlassContainer(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.star, color: Colors.yellow[300]),
                      SizedBox(width: 8),
                      Text(
                        'Parent Details',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  _buildDetailItem(
                    icon: Icons.person_outline,
                    label: 'Username',
                    value: parent.username,
                    color: Colors.amber,
                  ),
                  _buildDetailItem(
                    icon: Icons.work_outline,
                    label: 'Occupation',
                    value: parent.occupation,
                    color: Colors.blue,
                  ),
                  _buildDetailItem(
                    icon: Icons.favorite_border,
                    label: 'Relationship',
                    value: parent.relationship,
                    color: Colors.pink,
                  ),
                  _buildDetailItem(
                    icon: Icons.calendar_today,
                    label: 'Member Since',
                    value: DateFormat('MMM yyyy').format(DateTime.parse(parent.createdAt)),
                    color: Colors.purple,
                  ),
                  // Student Details Section
                  if (studentData != null) ...[
                    SizedBox(height: 24),
                    Row(
                      children: [
                        Icon(Icons.school, color: Colors.green[300]),
                        SizedBox(width: 8),
                        Text(
                          'Student Details',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    _buildDetailItem(
                      icon: Icons.badge,
                      label: 'Student ID',
                      value: studentId,
                      color: Colors.teal,
                    ),
                    _buildDetailItem(
                      icon: Icons.email_outlined,
                      label: 'Student Email',
                      value: studentData['student_email'] ?? 'N/A',
                      color: Colors.indigo,
                    ),
                    _buildDetailItem(
                      icon: Icons.phone_outlined,
                      label: 'Student Phone',
                      value: studentData['phone'] ?? 'N/A',
                      color: Colors.cyan,
                    ),
                  ],
                ],
              ),
            ),
            SizedBox(height: 16),
            // Inspirational Quote
            _buildGlassContainer(
              child: Column(
                children: [
                  Icon(Icons.lightbulb_outline, color: Colors.yellow[300], size: 32),
                  SizedBox(height: 12),
                  Text(
                    '"Trust your child\'s journey, even if it doesn\'t look like yours..."',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialTab(Parent parent) {
    return RefreshIndicator(
      onRefresh: () => Provider.of<ParentProvider>(context, listen: false).refreshData(),
      color: Colors.white,
      child: SingleChildScrollView(
        physics: AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            // Financial Overview Cards
            Row(
              children: [
                Expanded(
                  child: _buildGlassContainer(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.attach_money, color: Colors.green),
                                SizedBox(width: 8),
                                Text(
                                  'Financial Overview',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                parent.annualIncome != null ? 'Verified' : 'Not Specified',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Annual Income',
                          style: TextStyle(color: Colors.white70, fontSize: 14),
                        ),
                        Text(
                          parent.annualIncome != null
                              ? '₹${NumberFormat.compact().format(parent.annualIncome)}'
                              : 'Not specified',
                          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
                        ),
                        SizedBox(height: 12),
                        LinearProgressIndicator(
                          value: parent.annualIncome != null
                              ? (parent.annualIncome! / 1000000).clamp(0.0, 1.0)
                              : 0.0,
                          backgroundColor: Colors.white.withOpacity(0.2),
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                        ),
                        SizedBox(height: 8),
                        Text(
                          parent.annualIncome != null
                              ? 'Based on your declared income'
                              : 'Please update your financial information',
                          style: TextStyle(color: Colors.white70, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildGlassContainer(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.credit_card_outlined, color: Colors.blue, size: 48),
                        SizedBox(height: 16),
                        Text(
                          'Payment History',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Coming Soon',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 24),
            // Financial Tips
            _buildGlassContainer(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.trending_up, color: Colors.yellow[300]),
                      SizedBox(width: 8),
                      Text(
                        'Financial Planning Tips',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Column(
                    children: [
                      _buildTipItem('Consider setting up an education fund for your child\'s future'),
                      _buildTipItem('Explore tax benefits available for education expenses'),
                      _buildTipItem('Review school fee payment plans for better budgeting'),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build stat cards
  Widget _buildStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return _StatCard(
      icon: icon,
      label: label,
      value: value,
      color: color,
    );
  }

  // Helper method to build detail items
  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return _DetailItem(
      icon: icon,
      label: label,
      value: value,
      color: color,
    );
  }

  // Helper method to build tip items
  Widget _buildTipItem(String text) {
    return _TipItem(text);
  }
}

// Custom Sliver Header for Tabs
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate(this._tabBar);

  final PreferredSizeWidget _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height + 16;
  @override
  double get maxExtent => _tabBar.preferredSize.height + 16;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: Colors.white.withValues(alpha: 0.2)),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: _tabBar,
        ),
      ),
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}

// Reusable Stat Card Widget
class _StatCard extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;

  const _StatCard({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(height: 8),
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Reusable Detail Item Widget
class _DetailItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;

  const _DetailItem({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Icon(Icons.chevron_right, color: Colors.white.withValues(alpha: 0.5)),
        ],
      ),
    );
  }
}

// Tip Item Widget
class _TipItem extends StatelessWidget {
  final String text;

  const _TipItem(this.text);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(Icons.lightbulb_outline, color: Colors.yellow[300], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}