import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:lucide_flutter/lucide_flutter.dart';
import 'package:intl/intl.dart';

// --- Data Models for API Response ---

class VideoEvent {
  final String id;
  final String title;
  final String subject;
  final String recordingUrl;
  final DateTime date;

  // Fallback thumbnail URLs
  static const Map<String, String> _thumbnails = {
    'Physics': 'https://t4.ftcdn.net/jpg/04/38/69/77/360_F_438697752_bIJMNtw4TvQGqFbp5e2mR7RmaJG8e2kKFP.jpg',
    'Chemistry': 'https://img.freepik.com/free-vector/chalkboard-background-with-chemistry-information_23-2148159091.jpg',
    'Mathematics': 'https://img.freepik.com/free-vector/isometric-maths-material-background_23-2146146102.jpg',
    'Biology': 'https://t3.ftcdn.net/jpg/00/85/94/80/360_F_85948050_pnCz9BxRzGqFbp5e2mR7RmaJG8e2kKFP.jpg',
    'Fallback': 'https://img.freepik.com/free-vector/video-player-with-film-reel_23-2148557445.jpg',
  };

  VideoEvent({
    required this.id,
    required this.title,
    required this.subject,
    required this.recordingUrl,
    required this.date,
  });

  String get thumbnail => _thumbnails[subject] ?? _thumbnails['Fallback']!;

  factory VideoEvent.fromJson(Map<String, dynamic> json) {
    String capitalize(String s) {
      if (s.isEmpty) return s;
      return s[0].toUpperCase() + s.substring(1).toLowerCase();
    }
    
    final subject = capitalize(json['subject'] ?? 'General');
    return VideoEvent(
      id: json['id']?.toString() ?? UniqueKey().toString(),
      title: json['topic'] ?? 'Class on $subject',
      subject: subject,
      recordingUrl: json['recording_url'] ?? '',
      date: DateTime.tryParse(json['event_date'] ?? '') ?? DateTime.now(),
    );
  }
}

// --- Main Widget: RecordedVideosPage ---

class RecordedVideosPage extends StatefulWidget {
  const RecordedVideosPage({super.key});

  @override
  State<RecordedVideosPage> createState() => _RecordedVideosPageState();
}

class _RecordedVideosPageState extends State<RecordedVideosPage> {
  // State variables
  late Future<List<VideoEvent>> _videosFuture;
  final Map<String, List<VideoEvent>> _groupedVideos = {};
  List<String> _subjects = [];
  String? _selectedSubject;
  
  // Static user data for demonstration. 
  // In a real app, this would come from a login or state management solution.
  final String _userCourse = 'JEE';
  final String _userBatchName = 'A1';

  @override
  void initState() {
    super.initState();
    _videosFuture = _fetchAndProcessVideos();
  }

  // --- Data Fetching and Processing ---
  
  Future<List<VideoEvent>> _fetchAndProcessVideos() async {
    // IMPORTANT: Replace with your actual API endpoint
    const String apiUrl = "https://testing.sasthra.in/api/class-event-details";

    try {
      final response = await http.get(Uri.parse(apiUrl));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> events = data['events'] ?? [];
        
        // Filter and map events similar to the React component
        List<VideoEvent> videos = events
            .map((eventData) => VideoEvent.fromJson(eventData))
            .where((video) => video.recordingUrl.trim().isNotEmpty && video.recordingUrl != 'null')
            .toList();

        _groupVideosBySubject(videos);
        return videos;
      } else {
        throw Exception('Failed to load videos: Status code ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching videos: $e');
      throw Exception('Failed to load videos: $e');
    }
  }

  void _groupVideosBySubject(List<VideoEvent> videos) {
    _groupedVideos.clear();
    for (var video in videos) {
      if (!_groupedVideos.containsKey(video.subject)) {
        _groupedVideos[video.subject] = [];
      }
      _groupedVideos[video.subject]!.add(video);
    }
    
    // Order subjects like in the React component
    const orderJEE = ['Physics', 'Chemistry', 'Mathematics'];
    const orderNEET = ['Physics', 'Chemistry', 'Biology'];
    final courseOrder = _userCourse == 'NEET' ? orderNEET : orderJEE;

    _subjects = courseOrder.where((subject) => _groupedVideos.containsKey(subject)).toList();

    // Add any other subjects that might not be in the predefined order
    for (var subject in _groupedVideos.keys) {
      if (!_subjects.contains(subject)) {
        _subjects.add(subject);
      }
    }

    if (_subjects.isNotEmpty) {
      setState(() {
        _selectedSubject = _subjects.first;
      });
    }
  }

  // --- UI Building Methods ---

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: FutureBuilder<List<VideoEvent>>(
        future: _videosFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Text(
                  'Error loading videos: ${snapshot.error}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            );
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No recorded videos found.'));
          }

          return _buildContent();
        },
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          _buildSubjectFilter(),
          const SizedBox(height: 24),
          _buildVideosList(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Row(
              children: [
                 CircleAvatar(
                  backgroundColor: Color(0xFFE3F2FD), // student/10 color
                  child: Icon(LucideIcons.graduationCap, color: Color(0xFF1E88E5)), // student color
                ),
                SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Recorded Videos',
                      style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.black87),
                    ),
                    Text(
                      'Access your recorded classes anytime',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              ],
            ),
             // This can be expanded to show more info if needed
             Container(
               padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
               decoration: BoxDecoration(
                 color: const Color(0xFFE3F2FD),
                 borderRadius: BorderRadius.circular(8),
                 border: Border.all(color: const Color(0xFFBBDEFB))
               ),
               child: Row(
                 children: [
                   const Icon(LucideIcons.bookOpen, size: 16, color: Color(0xFF1E88E5)),
                   const SizedBox(width: 6),
                   Text(
                     '$_userCourse Preparation',
                     style: const TextStyle(fontWeight: FontWeight.w600, color: Color(0xFF1E88E5)),
                   ),
                 ],
               ),
             )
          ],
        ),
      ],
    );
  }
  
  Widget _buildSubjectFilter() {
    if (_subjects.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1E88E5), // student color
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3)
          )
        ]
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Subject',
            style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children: _subjects.map((subject) {
              bool isSelected = _selectedSubject == subject;
              return ChoiceChip(
                label: Text(subject),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedSubject = subject;
                    });
                  }
                },
                backgroundColor: Colors.white70,
                selectedColor: const Color(0xFFFFC107), // counselor color
                labelStyle: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                  fontWeight: FontWeight.w600,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(
                    color: isSelected ? Colors.transparent : Colors.grey.shade300,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildVideosList() {
    // Display videos for the selected subject
    if (_selectedSubject == null || !_groupedVideos.containsKey(_selectedSubject)) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Text('Please select a subject to see videos.'),
        ),
      );
    }
    
    final subjectVideos = _groupedVideos[_selectedSubject!]!;

    return Column(
       crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            '$_selectedSubject Videos (${subjectVideos.length})',
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2, // Adjust number of columns
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.85, // Adjust item aspect ratio
          ),
          itemCount: subjectVideos.length,
          itemBuilder: (context, index) {
            final video = subjectVideos[index];
            return _buildVideoCard(video);
          },
        ),
      ],
    );
  }

  Widget _buildVideoCard(VideoEvent video) {
    return GestureDetector(
      onTap: () {
        // Navigate to the video player page
        Navigator.of(context).push(MaterialPageRoute(
          builder: (_) => VideoPlayerPage(video: video),
        ));
      },
      child: Card(
        elevation: 4,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        clipBehavior: Clip.antiAlias,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Image.network(
                    video.thumbnail,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => const Icon(Icons.error),
                  ),
                  Container(color: Colors.black.withOpacity(0.2)),
                  const Center(
                    child: CircleAvatar(
                      backgroundColor: Colors.white,
                      radius: 24,
                      child: Icon(LucideIcons.play, color: Color(0xFF1E88E5), size: 28),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                 crossAxisAlignment: CrossAxisAlignment.start,
                 children: [
                   Text(
                     video.title,
                     style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                     maxLines: 2,
                     overflow: TextOverflow.ellipsis,
                   ),
                   const SizedBox(height: 4),
                   Text(
                     DateFormat.yMMMd().format(video.date), // Formats date e.g., "Sep 11, 2025"
                     style: const TextStyle(color: Colors.grey, fontSize: 12),
                   ),
                 ],
              ),
            )
          ],
        ),
      ),
    );
  }
}


// --- Video Player Page Widget ---
// Kept in the same file as requested

class VideoPlayerPage extends StatefulWidget {
  final VideoEvent video;

  const VideoPlayerPage({super.key, required this.video});

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  late VideoPlayerController _videoPlayerController;
  ChewieController? _chewieController;

  @override
  void initState() {
    super.initState();
    initializePlayer();
  }

  Future<void> initializePlayer() async {
    if (widget.video.recordingUrl.isEmpty) {
      debugPrint("Video URL is empty, cannot initialize player.");
      // Handle empty URL case, maybe show an error message
      return;
    }

    _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(widget.video.recordingUrl));
    
    await _videoPlayerController.initialize();
    
    _chewieController = ChewieController(
      videoPlayerController: _videoPlayerController,
      autoPlay: true,
      looping: false,
      placeholder: const Center(child: CircularProgressIndicator()),
      // Additional options
      allowFullScreen: true,
      aspectRatio: _videoPlayerController.value.aspectRatio,
      materialProgressColors: ChewieProgressColors(
        playedColor: const Color(0xFF1E88E5), // student color
        handleColor: const Color(0xFF1E88E5),
        bufferedColor: Colors.grey.shade400,
        backgroundColor: Colors.grey.shade700,
      ),
      errorBuilder: (context, errorMessage) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              "Error playing video: $errorMessage",
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
    
    setState(() {});
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _chewieController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.video.title),
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(color: Colors.white, fontSize: 18),
      ),
      body: Container(
        color: Colors.black,
        child: Center(
          child: _chewieController != null && _chewieController!.videoPlayerController.value.isInitialized
              ? Chewie(controller: _chewieController!)
              : const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 20),
                    Text('Loading Video...', style: TextStyle(color: Colors.white),),
                  ],
                ),
        ),
      ),
    );
  }
}