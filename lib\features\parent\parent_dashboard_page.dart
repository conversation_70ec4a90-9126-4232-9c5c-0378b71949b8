import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:ui';
import 'package:go_router/go_router.dart';
import '../../core/services/token_service.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';
import '../../core/theme/app_theme.dart';
import '../../core/widgets/base_page.dart';

// Parent Data Model
class Parent {
  final String username;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String occupation;
  final String relationship;
  final String createdAt;
  final double? annualIncome;
  final String studentId;
  final bool isActive;
  final Map<String, dynamic>? student;

  Parent({
    required this.username,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.occupation,
    required this.relationship,
    required this.createdAt,
    this.annualIncome,
    required this.studentId,
    required this.isActive,
    this.student,
  });

  factory Parent.fromJson(Map<String, dynamic> json) {
    return Parent(
      username: json['username'] ?? 'Unknown',
      firstName: json['first_name'] ?? 'Unknown',
      lastName: json['last_name'] ?? 'Unknown',
      email: json['parent_email'] ?? 'Unknown',
      phone: json['phone'] ?? 'Unknown',
      occupation: json['occupation'] ?? 'Unknown',
      relationship: json['relationship'] ?? 'Unknown',
      createdAt: json['created_at'] ?? 'Unknown',
      annualIncome: json['annual_income_inr']?.toDouble(),
      studentId: json['student_id'] ?? 'Unknown',
      isActive: json['is_active'] ?? false,
      student: json['student'],
    );
  }
}

// Parent Provider for state management
class ParentProvider with ChangeNotifier {
  Parent? _parent;
  bool _isLoading = true;
  bool _isError = false;
  String _errorMessage = '';

  Parent? get parent => _parent;
  bool get isLoading => _isLoading;
  bool get isError => _isError;
  String get errorMessage => _errorMessage;

  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  Future<void> fetchParentData() async {
    _setLoading(true);
    _setError(false, '');

    try {
      AppLogger.info('Fetching parent dashboard data');
      final currentToken = await _tokenService.getToken();

      if (currentToken == null || !(await _tokenService.isTokenValid())) {
        AppLogger.warning('Token validation failed');
        _setError(true, 'Authentication token has expired. Please log in again.');
        return;
      }

      final data = await _apiService.fetchParentDashboard();
      final parentData = Parent.fromJson(data['parent'] ?? data);

      _parent = parentData;
      _setLoading(false);
      AppLogger.info('Parent dashboard data loaded successfully');
    } catch (e) {
      AppLogger.error('Failed to fetch parent data: $e');
      String msg = 'Unable to fetch parent details. Please try again.';
      if (e.toString().contains('401')) {
        msg = 'Session expired. Please log in again.';
      } else if (e.toString().contains('Network')) {
        msg = 'Network error. Please check your connection.';
      }
      _setError(true, msg);
      _setLoading(false);
    }
  }

  Future<void> refreshData() async {
    AppLogger.userAction('Parent dashboard refresh', {'timestamp': DateTime.now().toIso8601String()});
    await fetchParentData();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(bool error, String message) {
    _isError = error;
    _errorMessage = message;
    notifyListeners();
  }
}

// Parent Dashboard Page - Main widget for parent role dashboard
class ParentDashboardPage extends StatefulWidget {
  const ParentDashboardPage({super.key});

  @override
  State<ParentDashboardPage> createState() => _ParentDashboardPageState();
}

class _ParentDashboardPageState extends State<ParentDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ParentProvider _parentProvider;

  @override
  void initState() {
    super.initState();
    AppLogger.navigation('Parent Dashboard', 'Page initialized');
    _tabController = TabController(length: 3, vsync: this);
    _parentProvider = ParentProvider();
    _parentProvider.fetchParentData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _parentProvider.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _parentProvider,
      child: BasePage(
        title: 'Parent Dashboard',
        subtitle: 'Manage your child\'s education journey',
        breadcrumbs: const ['Dashboard', 'Parent'],
        actions: [
          IconButton(
            onPressed: () {
              AppLogger.userAction('Parent dashboard refresh', {'source': 'app_bar'});
              _parentProvider.refreshData();
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
        child: Consumer<ParentProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading) {
              return _buildLoadingView();
            }

            if (provider.isError) {
              return _buildErrorView(provider);
            }

            return _buildDashboardContent(provider.parent);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading parent dashboard...'),
        ],
      ),
    );
  }

  Widget _buildErrorView(ParentProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Error Loading Dashboard',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            provider.errorMessage,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              AppLogger.userAction('Parent dashboard retry', {'error': provider.errorMessage});
              provider.refreshData();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardContent(Parent? parent) {
    if (parent == null) {
      return const Center(child: Text('No parent data available'));
    }

    return Column(
      children: [
        // Tab Bar
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
            Tab(icon: Icon(Icons.school), text: 'Student'),
            Tab(icon: Icon(Icons.attach_money), text: 'Financial'),
          ],
        ),
        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(parent),
              _buildStudentTab(parent),
              _buildFinancialTab(parent),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewTab(Parent parent) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome, ${parent.firstName} ${parent.lastName}!',
                    style: AppTheme.headingMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Here\'s an overview of your account and your child\'s progress.',
                    style: AppTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Quick Stats
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
            children: [
              _buildStatCard(
                icon: Icons.person,
                title: 'Parent',
                value: '${parent.firstName} ${parent.lastName}',
                color: Colors.blue,
              ),
              _buildStatCard(
                icon: Icons.phone,
                title: 'Contact',
                value: parent.phone,
                color: Colors.green,
              ),
              _buildStatCard(
                icon: Icons.email,
                title: 'Email',
                value: parent.email,
                color: Colors.orange,
              ),
              _buildStatCard(
                icon: Icons.work,
                title: 'Occupation',
                value: parent.occupation,
                color: Colors.purple,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStudentTab(Parent parent) {
    final studentData = parent.student;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (studentData != null) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Student Information',
                      style: AppTheme.headingMedium,
                    ),
                    const SizedBox(height: 16),
                    _buildInfoRow('Name', '${studentData['first_name'] ?? ''} ${studentData['last_name'] ?? ''}'),
                    _buildInfoRow('Student ID', studentData['id']?.toString() ?? 'N/A'),
                    _buildInfoRow('Email', studentData['student_email'] ?? 'N/A'),
                    _buildInfoRow('Phone', studentData['phone'] ?? 'N/A'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Quick Actions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quick Actions',
                      style: AppTheme.headingMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              AppLogger.userAction('Navigate to attendance', {'from': 'parent_dashboard'});
                              context.go('/dashboard/parent/student_attendance');
                            },
                            icon: const Icon(Icons.event_available),
                            label: const Text('View Attendance'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              AppLogger.userAction('Navigate to student info', {'from': 'parent_dashboard'});
                              context.go('/dashboard/parent/student_info');
                            },
                            icon: const Icon(Icons.info),
                            label: const Text('Student Info'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(Icons.info_outline, size: 48, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'No Student Information Available',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Student information is not available at this time.',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFinancialTab(Parent parent) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Financial Information',
                    style: AppTheme.headingMedium,
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow(
                    'Annual Income',
                    parent.annualIncome != null
                        ? '₹${NumberFormat.compact().format(parent.annualIncome)}'
                        : 'Not specified',
                  ),
                  _buildInfoRow('Status', parent.annualIncome != null ? 'Verified' : 'Pending'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Financial Tips',
                    style: AppTheme.headingMedium,
                  ),
                  const SizedBox(height: 16),
                  _buildTipItem('Consider setting up an education fund for your child\'s future'),
                  _buildTipItem('Explore tax benefits available for education expenses'),
                  _buildTipItem('Review school fee payment plans for better budgeting'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTheme.bodySmall.copyWith(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTheme.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTipItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.lightbulb_outline, size: 20, color: Colors.amber),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: AppTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
