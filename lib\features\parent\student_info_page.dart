import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:ui';

// Assuming these services and utilities exist in your project
import '../../core/services/token_service.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';

class StudentInfo extends StatefulWidget {
  const StudentInfo({Key? key}) : super(key: key);

  @override
  _StudentInfoState createState() => _StudentInfoState();
}

class _StudentInfoState extends State<StudentInfo> {
  Map<String, dynamic>? student;
  bool isLoading = true;
  bool isError = false;
  String errorMessage = '';

  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    fetchStudentDetails();
  }

  Future<void> refreshStudentData() async {
    await fetchStudentDetails();
  }

  Future<void> fetchStudentDetails() async {
    setState(() {
      isLoading = true;
      isError = false;
    });

    try {
      final currentToken = await _tokenService.getToken();

      if (currentToken == null || !(await _tokenService.isTokenValid())) {
        setState(() {
          isError = true;
          errorMessage = 'Authentication token has expired. Please log in again.';
          isLoading = false;
        });
        return;
      }

      final data = await _apiService.fetchParentDashboard();
      final studentData = data['student'] ?? data['students']?.first ?? data;

      setState(() {
        student = studentData;
        isLoading = false;
      });
    } catch (e) {
      String msg = 'Unable to fetch student details. Please try again.';
      if (e.toString().contains('401')) {
        msg = 'Session expired. Please log in again.';
      } else if (e.toString().contains('Network')) {
        msg = 'Network error. Please check your connection.';
      }
      setState(() {
        isError = true;
        errorMessage = msg;
        isLoading = false;
      });
    }
  }

  String _formatDate(dynamic dob) {
    if (dob == null || dob.toString().isEmpty) {
      return 'N/A';
    }
    try {
      DateTime parsed = DateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", "en_US").parseUtc(dob);
      return DateFormat('dd/MM/yyyy').format(parsed.toLocal());
    } catch (_) {
      try {
        DateTime parsed = DateTime.parse(dob);
        return DateFormat('dd/MM/yyyy').format(parsed);
      } catch (_) {
        return 'Invalid Date';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
            stops: [0.0, 1.0],
          ),
        ),
        child: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (isLoading) {
      return Center(
        child: _buildGlassContainer(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: const [
              SizedBox(
                width: 48,
                height: 48,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              SizedBox(height: 16),
              Text(
                'Loading student information...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (isError) {
      return Center(
        child: _buildGlassContainer(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("⚠️", style: TextStyle(fontSize: 40)),
              const SizedBox(height: 12),
              const Text(
                'Error Loading Student Information',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  decoration: TextDecoration.none,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                errorMessage,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  decoration: TextDecoration.none,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: refreshStudentData,
                icon: const Icon(Icons.refresh, color: Colors.white),
                label: const Text(
                  'Retry',
                  style: TextStyle(
                    color: Colors.white,
                    decoration: TextDecoration.none,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.2),
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: const BorderSide(color: Colors.white30),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (student == null) {
      return Center(
        child: _buildGlassContainer(
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("👤", style: TextStyle(fontSize: 40)),
              SizedBox(height: 12),
              Text(
                "No student information available",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: refreshStudentData,
      color: Colors.white,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildGlassContainer(
              child: Column(
                children: [
                  _buildProfileHeader(),
                  const SizedBox(height: 20),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      if (constraints.maxWidth > 600) {
                        return IntrinsicHeight(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: _buildPersonalInformation(),
                              ),
                              const VerticalDivider(color: Colors.white, thickness: 1, width: 40, indent: 20, endIndent: 20),
                              Expanded(
                                child: _buildAcademicRecords(),
                              ),
                            ],
                          ),
                        );
                      } else {
                        return Column(
                          children: [
                            _buildPersonalInformation(),
                            const SizedBox(height: 20),
                            const Divider(color: Colors.white, height: 20),
                            const SizedBox(height: 20),
                            _buildAcademicRecords(),
                          ],
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // --- Helper Widgets ---

  Widget _buildGlassContainer({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            padding: const EdgeInsets.all(24),
            child: child,
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Column(
      children: [
        const CircleAvatar(
          radius: 35,
          backgroundColor: Colors.white54,
          child: Icon(Icons.school, size: 40, color: Colors.white),
        ),
        const SizedBox(height: 10),
        const Text(
          "Student Profile",
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            decoration: TextDecoration.none,
          ),
        ),
        const SizedBox(height: 10),
        Container(
          height: 2,
          width: 60,
          color: Colors.white,
        ),
      ],
    );
  }

  Widget _buildPersonalInformation() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Personal Information"),
        _buildInfoItem("Full Name", "${student!['first_name'] ?? ''} ${student!['last_name'] ?? ''}"),
        _buildInfoItem("Student ID", student!['id']?.toString() ?? "N/A"),
        _buildInfoItem("Email Address", student!['student_email'] ?? "N/A"),
        _buildInfoItem("Phone Number", student!['phone'] ?? "N/A"),
        _buildInfoItem("Date of Birth", _formatDate(student!['dob'])),
        _buildInfoItem("Religion", student!['religion'] ?? "N/A"),
      ],
    );
  }

  Widget _buildAcademicRecords() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle("Academic Records"),
        _buildMarksCard("10th Grade Marks", student!['marks_10th']?.toString() ?? "N/A", "📚"),
        const SizedBox(height: 12),
        _buildMarksCard("12th Grade Marks", student!['marks_12th']?.toString() ?? "N/A", "🎯"),
        const SizedBox(height: 20),
        _buildQuoteCard(),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            decoration: TextDecoration.none,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 1,
          width: 150,
          color: Colors.white,
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildQuoteCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: const Text(
        "\"Trust your child's journey, even if it doesn't look like yours...\"",
        style: TextStyle(
          fontStyle: FontStyle.italic,
          color: Colors.white,
          fontSize: 15,
          decoration: TextDecoration.none,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.only(top: 5, right: 8),
            decoration: const BoxDecoration(
              color: Colors.white70,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                    decoration: TextDecoration.none,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.none,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMarksCard(String title, String value, String emoji) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white30, width: 1),
      ),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                  decoration: TextDecoration.none,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
          Text(
            emoji,
            style: const TextStyle(fontSize: 32),
          ),
        ],
      ),
    );
  }
}