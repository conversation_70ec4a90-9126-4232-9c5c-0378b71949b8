/// AppConfig - Application Configuration Class
///
/// English: This class contains all the configuration constants and settings for the Sasthra
/// mobile application. It centralizes API endpoints, authentication settings, storage keys,
/// UI configurations, and role-based settings. This ensures consistent configuration across
/// the entire application and makes it easy to modify settings from a single location.
///
/// Tanglish: Inga naama app ku vendiya ella configuration um centralized aaga irukku.
/// API endpoints, authentication settings, storage keys, UI settings - ellam inga define
/// pannirukkom. Oru place la irundhu ella settings um control panna mudiyum.
///
/// Configuration Categories:
/// - API Configuration (Base URLs, endpoints, timeouts)
/// - Authentication Settings (Token management, session handling)
/// - Storage Keys (Local storage identifiers)
/// - UI Settings (Border radius, animations, elevations)
/// - Role Management (Supported roles, display names, colors)
/// - Environment Settings (Debug/Production configurations)
///
/// Usage: AppConfig.baseUrl, AppConfig.supportedRoles, AppConfig.tokenKey
class AppConfig {
  // static const String baseUrl = 'https://sasthra.in';
  static const String baseUrl = 'https://testing.sasthra.in';
  static const String apiVersion = 'v1';
  static const String appName = 'Sasthra';
  static const String appVersion = '1.0.0';


  // API Endpoints
  static const String loginEndpoint = '/login';
  static const String verifyOtpEndpoint = '/verify-otp';
  static const String forgetPasswordEndpoint = '/forgot_password';
  static const String resetPasswordEndpoint = '/reset_password';
  static const String verifyTokenEndpoint = '/verify-token';
  static const String validateSessionEndpoint = '/validate-session';
  static const String logoutEndpoint = '/logout';
  static const String menuEndpoint = '/menu';
  static const String chatBotEndpoint = '/query';
  static const String problemSolverEndpoint = '/doubt_solver';
  static const String youtubeSearchEndpoint = '/youtube_search';
  static const String webSearchEndpoint = '/web_search';
  static const String generateAudioEndpoint = '/generate_audio';

  static const String examNamesEndpoint = '/exam_names';
  static const String examModulesEndpoint = '/exam_modules';
  static const String examUnitsEndpoint = '/units';
  static const String examSubtopicsEndpoint = '/subtopics';
  static const String startBioTestEndpoint = '/start-test';
  static const String startChemistryTestEndpoint = '/start-test-chemistry';
  static const String startMathTestEndpoint = '/start-test-maths';
  static const String startPhysicsTestEndpoint = '/start-test-pyhsics';
  static const String verifyFaceEndpoint = '/verify-face';

  // Community Endpoints
  static const String getThreadsEndpoint = '/get_threads';
  static const String getCommunityImagesEndpoint = '/community_get_images';
  static const String getCommunityImageByIdEndpoint =
      '/api/get_community_images'; // {imageId} in service
  static const String createThreadEndpoint = '/post_threads';
  static const String addReplyEndpoint = '/thread/subthread';
  static const String uploadCommunityImageEndpoint = '/community_images';
  static const String addThreadToImageEndpoint =
      '/api/post_community_images'; // {imageId}/thread in service
  static const String addReplyToImageThreadEndpoint =
      '/api/community_images'; // {imageId}/thread/{parentThreadId}/reply in service
  static const String verifyFaceEndpoint = '/verify-face';

  // Community Endpoints
  static const String getThreadsEndpoint = '/get_threads';
  static const String getCommunityImagesEndpoint = '/community_get_images';
  static const String getCommunityImageByIdEndpoint =
      '/api/get_community_images'; // {imageId} in service
  static const String createThreadEndpoint = '/post_threads';
  static const String addReplyEndpoint = '/thread/subthread';
  static const String uploadCommunityImageEndpoint = '/community_images';
  static const String addThreadToImageEndpoint =
      '/api/post_community_images'; // {imageId}/thread in service
  static const String addReplyToImageThreadEndpoint =
      '/api/community_images'; // {imageId}/thread/{parentThreadId}/reply in service

  // Student Panel Endpoints
  // Onboarding Assessment Endpoints
  static const String startAssessmentEndpoint = '/start-assessment';
  static const String submitAssessmentEndpoint = '/submit-assessment';
  static const String checkStudentAssessmentEndpoint =
      '/check-student-assessment';
  static const String completeStudentAssessmentEndpoint =
      '/complete-student-assessment';
  static const String checkStudentAssessmentEndpoint =
      '/check-student-assessment';
  static const String completeStudentAssessmentEndpoint =
      '/complete-student-assessment';
  // Student Dashboard Endpoints
  static const String studentDashboardEndpoint = '/student-dashboard';
<<<<<<< HEAD
  // PDF Question Generator Endpoints
  static const String uploadQuestionEndpoint = '/upload_question';
  static const String generatePdfEndpoint = '/generate_pdf';
=======
  // Parent Dashboard Endpoints
  static const String parentDashboardEndpoint = '/parent-dashboard';
  static const String studentDashboardEndpoint = '/student-dashboard';
  // PDF Question Generator Endpoints
  static const String uploadQuestionEndpoint = '/upload_question';
  static const String generatePdfEndpoint = '/generate_pdf'; 
  // Ebook Endpoint
  
  // AI Tutor Endpoints
   static const String explainSlidesEndpoint = "/studentpanel/physicsapi/explain_slides";


>>>>>>> ebac7d8857007fcc80ec35735a5f84e39ddaaa40
  static const String generatePdfEndpoint = '/generate_pdf';
  // Token Configuration
  static const Duration tokenRefreshInterval = Duration(hours: 5);
  static const Duration tokenExpiryBuffer = Duration(minutes: 30);
  static const int maxRetryAttempts = 3;


  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String sessionIdKey = 'session_id';
  static const String lastTokenRefreshKey = 'last_token_refresh';
  static const String menuCacheKey = 'menu_cache';
  static const String studentDashboardKey = 'student_dashboard';

<<<<<<< HEAD
=======
  
  
>>>>>>> ebac7d8857007fcc80ec35735a5f84e39ddaaa40
  static const String studentDashboardKey = 'student_dashboard';

  // App Settings
  static const bool isDebug = true;
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration connectionTimeout = Duration(seconds: 15);


  // UI Configuration
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  static const Duration animationDuration = Duration(milliseconds: 300);


  // Supported Roles
  static const List<String> supportedRoles = [
    'faculty',
    'kota_teacher',
    'student',
    'director',
    'mendor',
    'center_counselor',
    'parent',
  ];


  // Role Display Names
  static const Map<String, String> roleDisplayNames = {
    'faculty': 'Faculty',
    'kota_teacher': 'Kota Teacher',
    'student': 'Student',
    'director': 'Director',
    'mendor': 'Mentor',
    'center_counselor': 'Center Counselor',
    'parent': 'Parent',
  };


  // Role Colors
  static const Map<String, int> roleColors = {
    'faculty': 0xFF2196F3,
    'kota_teacher': 0xFF4CAF50,
    'student': 0xFFFF9800,
    'director': 0xFF9C27B0,
    'mendor': 0xFF00BCD4,
    'center_counselor': 0xFFE91E63,
    'parent': 0xFF795548,
  };


  // Environment specific configurations
  static String get apiBaseUrl {
    if (isDebug) {
      return baseUrl;
    }
    return baseUrl;
  }


  static Map<String, String> get defaultHeaders => {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Sasthra-Mobile-App/$appVersion',
      };
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Sasthra-Mobile-App/$appVersion',
      };
}
