import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import '../../../../core/config/app_config.dart';
import '../controller/problem_solver_controller.dart';
import '../model/search_result_models.dart';
import 'package:flutter_math_fork/flutter_math.dart';

class ProblemSolverPage extends StatefulWidget {
  const ProblemSolverPage({super.key});

  @override
  State<ProblemSolverPage> createState() => _ProblemSolverPageState();
}

class _ProblemSolverPageState extends State<ProblemSolverPage> with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showSnackbar(String title, String message, ContentType type) {
    final snackBar = SnackBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      behavior: SnackBarBehavior.floating,
      content: AwesomeSnackbarContent(
        title: title,
        message: message,
        contentType: type,
      ),
      duration: const Duration(seconds: 3),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: const Size(360, 640));
    return ChangeNotifierProvider(
      create: (_) => ProblemSolverController(),
      child: Scaffold(
        backgroundColor: const Color(0xFFF8FAFC),
        appBar: _buildModernAppBar(),
        body: Consumer<ProblemSolverController>(
          builder: (context, controller, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  _buildEnhancedSettingsPanel(controller),
                  Expanded(
                    child: _buildMainContent(controller),
                  ),
                  _buildEnhancedInputPanel(controller),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.psychology, size: 24.sp, color: Colors.white),
          ),
          SizedBox(width: 12.w),
          Flexible(
            child: Text(
              'AI Problem Solver',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 20.sp,
                letterSpacing: 0.5,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
      backgroundColor: const Color(0xFF6366F1),
      foregroundColor: Colors.white,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedSettingsPanel(ProblemSolverController controller) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(isTablet ? 24.w : 20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 20.r,
            offset: Offset(0, 4.h),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8.w),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6366F1).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Icon(Icons.tune, color: const Color(0xFF6366F1), size: 20.sp),
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      'Settings',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20.h),
                Row(
                  children: [
                    Container(
                      constraints: BoxConstraints(maxWidth: isTablet ? 200.w : 150.w),
                      child: _buildModernDropdown(
                        controller,
                        'Mode',
                        controller.selectedMode,
                        controller.modes,
                        Icons.psychology,
                        const Color(0xFF6366F1),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Container(
                      constraints: BoxConstraints(maxWidth: isTablet ? 200.w : 150.w),
                      child: _buildModernDropdown(
                        controller,
                        'Language',
                        controller.selectedLanguage,
                        controller.languages,
                        Icons.language,
                        const Color(0xFF059669),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                _buildModernActionButtons(controller),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernDropdown(
    ProblemSolverController controller,
    String type,
    String currentValue,
    List<String> items,
    IconData icon,
    Color color,
  ) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: color.withOpacity(0.2)),
        color: color.withOpacity(0.05),
      ),
      child: DropdownButtonFormField<String>(
        isDense: true,
        value: currentValue,
        decoration: InputDecoration(
          labelText: type,
          labelStyle: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 14.sp,
          ),
          prefixIcon: Container(
            margin: EdgeInsets.all(8.w),
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(icon, color: color, size: 20.sp),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
        ),
        items: items.map<DropdownMenuItem<String>>((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(
              value.toUpperCase(),
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 14.sp,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {
          if (type == 'Mode') {
            controller.setMode(newValue);
          } else {
            controller.setLanguage(newValue);
          }
        },
        dropdownColor: Colors.white,
        isExpanded: true,
      ),
    );
  }

  Widget _buildModernActionButtons(ProblemSolverController controller) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            constraints: BoxConstraints(maxWidth: isTablet ? 180.w : 140.w),
            child: _buildModernActionButton(
              'YouTube',
              Icons.play_circle_filled,
              const LinearGradient(colors: [Color(0xFFEF4444), Color(0xFFDC2626)]),
              () => _handleSearch(controller, SearchType.youtubeSearch),
              controller.isLoading && controller.lastSearchType == SearchType.youtubeSearch,
            ),
          ),
          SizedBox(width: isTablet ? 16.w : 12.w),
          Container(
            constraints: BoxConstraints(maxWidth: isTablet ? 180.w : 140.w),
            child: _buildModernActionButton(
              'Web',
              Icons.search,
              const LinearGradient(colors: [Color(0xFF059669), Color(0xFF047857)]),
              () => _handleSearch(controller, SearchType.webSearch),
              controller.isLoading && controller.lastSearchType == SearchType.webSearch,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernActionButton(
    String label,
    IconData icon,
    Gradient gradient,
    VoidCallback onPressed,
    bool isLoading,
  ) {
    return Container(
      height: 48.h,
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(14.r),
        boxShadow: [
          BoxShadow(
            color: gradient.colors.first.withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 8.r,
            offset: Offset(0, 4.h),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading
            ? SizedBox(
                width: 16.w,
                height: 16.h,
                child: CircularProgressIndicator(strokeWidth: 2.w, color: Colors.white),
              )
            : Icon(icon, size: 18.sp),
        label: Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14.sp,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14.r)),
        ),
      ),
    );
  }

  Widget _buildMainContent(ProblemSolverController controller) {
    if (controller.lastSearchType == SearchType.doubtSolver) {
      return _buildChatInterface(controller);
    } else if (controller.lastSearchType == SearchType.youtubeSearch) {
      return _buildYoutubeResults(controller);
    } else if (controller.lastSearchType == SearchType.webSearch) {
      return _buildWebResults(controller);
    } else {
      return _buildWelcomeScreen();
    }
  }

  Widget _buildWelcomeScreen() {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(isTablet ? 32.w : 24.w),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                ),
                borderRadius: BorderRadius.circular(24.r),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF6366F1).withOpacity(0.3),
                    spreadRadius: 0,
                    blurRadius: 20.r,
                    offset: Offset(0, 8.h),
                  ),
                ],
              ),
              child: Icon(Icons.psychology, size: isTablet ? 80.sp : 64.sp, color: Colors.white),
            ),
            SizedBox(height: 24.h),
            Text(
              'Welcome to AI Problem Solver',
              style: TextStyle(
                fontSize: isTablet ? 32.sp : 28.sp,
                fontWeight: FontWeight.w700,
                color: const Color(0xFF1F2937),
                letterSpacing: -0.5,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 12.h),
            Text(
              'Enter your question below and choose an action',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                '✨ Ask anything, get instant help',
                style: TextStyle(
                  color: const Color(0xFF6366F1),
                  fontWeight: FontWeight.w500,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatInterface(ProblemSolverController controller) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 20.r,
            offset: Offset(0, 4.h),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(isTablet ? 24.w : 20.w),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF8B5CF6), Color(0xFF7C3AED)],
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Icon(Icons.psychology, color: Colors.white, size: 20.sp),
                ),
                SizedBox(width: 12.w),
                Flexible(
                  child: Text(
                    'AI Tutor Chat',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    '${controller.selectedMode.toUpperCase()} • ${controller.selectedLanguage.toUpperCase()}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _buildChatMessages(controller),
          ),
        ],
      ),
    );
  }

  Widget _buildChatMessages(ProblemSolverController controller) {
    List<Widget> messages = [];
    if (controller.doubtText.isNotEmpty || controller.imageFile != null || controller.audioPath != null) {
      messages.add(_buildUserMessage(controller));
    }
    if (controller.response.isNotEmpty) {
      messages.add(_buildAIMessage(controller.response));
    }
    if (controller.history != null && controller.history!.isNotEmpty) {
      for (var msg in controller.history!) {
        if (msg['role'] == 'user') {
          messages.add(_buildHistoryUserMessage(msg['content']));
        } else if (msg['role'] == 'assistant') {
          messages.add(_buildHistoryAIMessage(msg['content']));
        }
      }
    }
    if (controller.error.isNotEmpty) {
      messages.add(_buildErrorMessage(controller.error));
    }
    if (messages.isEmpty) {
      return Center(
        child: Text(
          'Start a conversation with the AI tutor',
          style: TextStyle(color: Colors.grey[500], fontSize: 16.sp),
        ),
      );
    }
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(16.w),
      itemCount: messages.length,
      itemBuilder: (context, index) => messages[index],
    );
  }

  Widget _buildUserMessage(ProblemSolverController controller) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h, left: isTablet ? 80.w : 50.w),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
          ),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
            bottomLeft: Radius.circular(20.r),
            bottomRight: Radius.circular(4.r),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF6366F1).withOpacity(0.3),
              spreadRadius: 0,
              blurRadius: 8.r,
              offset: Offset(0, 2.h),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (controller.doubtText.isNotEmpty)
              Text(
                controller.doubtText,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  height: 1.4,
                ),
              ),
            if (controller.imageFile != null) ...[
              SizedBox(height: 12.h),
              ClipRRect(
                borderRadius: BorderRadius.circular(12.r),
                child: Image.file(
                  controller.imageFile!,
                  height: isTablet ? 150.h : 120.h,
                  width: isTablet ? 220.w : 180.w,
                  fit: BoxFit.cover,
                ),
              ),
            ],
            if (controller.audioPath != null) ...[
              SizedBox(height: 12.h),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.audiotrack, color: Colors.white, size: 18.sp),
                    SizedBox(width: 8.w),
                    Text(
                      'Audio message',
                      style: TextStyle(color: Colors.white, fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _preprocessResponse(String response) {
    String cleaned = response
        .replaceAll(RegExp(r'\$\s+\$'), r'$$')
        .replaceAll(RegExp(r'\r\n|\r'), '\n')
        .replaceAllMapped(RegExp(r'(\${1,2})([\s\S]*?)\1'), (match) {
          return match.group(0)!.replaceAll('\n', ' ');
        }).trim();
    return cleaned;
  }

  Widget _buildAIMessage(String message) {
    final processedText = _preprocessResponse(message);
    if (processedText.isEmpty) {
      return Text(
        'Received an empty response.',
        style: TextStyle(color: Colors.grey, fontSize: 16.sp),
      );
    }
    final paragraphs = processedText.split(RegExp(r'\n\s*\n+'));
    List<Widget> contentWidgets = [];
    for (final paragraph in paragraphs) {
      if (paragraph.trim().isEmpty) continue;
      contentWidgets.add(_buildParagraphWidget(paragraph.trim()));
    }
    if (contentWidgets.isEmpty) {
      return Text(
        'No content to display',
        style: TextStyle(color: Colors.grey, fontSize: 16.sp),
      );
    }
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h, right: 50.w),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: const Color(0xFFF8FAFC),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
            bottomLeft: Radius.circular(4.r),
            bottomRight: Radius.circular(20.r),
          ),
          border: Border.all(color: const Color(0xFFE5E7EB)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(6.w),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                    ),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(Icons.psychology, size: 16.sp, color: Colors.white),
                ),
                SizedBox(width: 8.w),
                Text(
                  'AI Tutor',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF6366F1),
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: contentWidgets,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParagraphWidget(String paragraph) {
    List<Widget> inlineWidgets = [];
    final RegExp latexRegex = RegExp(r'(\$\$.*?\$\$)|(\$.*?\$)');
    int lastMatchEnd = 0;

    for (final match in latexRegex.allMatches(paragraph)) {
      if (match.start > lastMatchEnd) {
        inlineWidgets.add(Text(
          paragraph.substring(lastMatchEnd, match.start),
          style: TextStyle(color: const Color(0xFF374151), fontSize: 16.sp, height: 1.5),
        ));
      }
      final latexText = match.group(0)!;
      final bool isDisplayMode = latexText.startsWith(r'$$');
      final String cleanedLatex = latexText
          .substring(isDisplayMode ? 2 : 1, latexText.length - (isDisplayMode ? 2 : 1))
          .trim();
      if (cleanedLatex.isNotEmpty) {
        inlineWidgets.add(
          Math.tex(
            cleanedLatex,
            mathStyle: isDisplayMode ? MathStyle.display : MathStyle.text,
            textStyle: TextStyle(fontSize: 16.sp),
            onErrorFallback: (err) {
              print("LaTeX Rendering Error: ${err.message} for \"$cleanedLatex\"");
              return Text(
                '[$cleanedLatex]',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold, fontSize: 16.sp),
              );
            },
          ),
        );
      }
      lastMatchEnd = match.end;
    }
    if (lastMatchEnd < paragraph.length) {
      inlineWidgets.add(Text(
        paragraph.substring(lastMatchEnd),
        style: TextStyle(color: const Color(0xFF374151), fontSize: 16.sp, height: 1.5),
      ));
    }
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        runSpacing: 8.h,
        spacing: 4.w,
        children: inlineWidgets,
      ),
    );
  }

  Widget _buildHistoryUserMessage(String message) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        margin: EdgeInsets.only(bottom: 8.h, left: isTablet ? 80.w : 50.w),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.deepPurple.withOpacity(0.7),
          borderRadius: BorderRadius.circular(15.r),
        ),
        child: Text(
          message,
          style: TextStyle(color: Colors.white, fontSize: 14.sp),
        ),
      ),
    );
  }

  Widget _buildHistoryAIMessage(String message) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.only(bottom: 8.h, right: isTablet ? 80.w : 50.w),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(15.r),
        ),
        child: Text(
          message,
          style: TextStyle(fontSize: 14.sp, height: 1.3),
        ),
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600], size: 20.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              error,
              style: TextStyle(color: Colors.red[600], fontSize: 14.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYoutubeResults(ProblemSolverController controller) {
    if (controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (controller.youtubeResults.isEmpty) {
      return _buildEmptyState('No YouTube videos found', Icons.video_library);
    }
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.play_circle_filled, color: Colors.red, size: 20.sp),
                SizedBox(width: 8.w),
                Flexible(
                  child: Text(
                    'YouTube Results',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  '${controller.youtubeResults.length} videos',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.red.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16.w),
              itemCount: controller.youtubeResults.length,
              itemBuilder: (context, index) {
                final result = controller.youtubeResults[index];
                return _buildYoutubeCard(result, controller);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYoutubeCard(YoutubeSearchResult result, ProblemSolverController controller) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Card(
      elevation: 2,
      margin: EdgeInsets.only(bottom: 12.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: () async {
          try {
            await controller.launchURL('https://www.youtube.com/watch?v=${result.videoId}');
            _showSnackbar('Success', 'Opening YouTube video', ContentType.success);
          } catch (e) {
            _showSnackbar('Error', 'Failed to open YouTube video: $e', ContentType.failure);
          }
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(12.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: result.thumbnailUrl.isNotEmpty
                    ? Image.network(
                        result.thumbnailUrl,
                        width: isTablet ? 150.w : 120.w,
                        height: isTablet ? 100.h : 90.h,
                        fit: BoxFit.cover,
                        errorBuilder: (c, o, s) => Container(
                          width: isTablet ? 150.w : 120.w,
                          height: isTablet ? 100.h : 90.h,
                          color: Colors.grey[300],
                          child: Icon(Icons.video_library, size: 40.sp, color: Colors.grey[600]),
                        ),
                      )
                    : Container(
                        width: isTablet ? 150.w : 120.w,
                        height: isTablet ? 100.h : 90.h,
                        color: Colors.grey[300],
                        child: Icon(Icons.video_library, size: 40.sp, color: Colors.grey[600]),
                      ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      result.title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16.sp,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      result.channelTitle,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(Icons.play_arrow, size: 16.sp, color: Colors.red),
                        SizedBox(width: 4.w),
                        Text(
                          'Watch on YouTube',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWebResults(ProblemSolverController controller) {
    if (controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (controller.webResults.isEmpty) {
      return _buildEmptyState('No web results found', Icons.search);
    }
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.search, color: Colors.green, size: 20.sp),
                SizedBox(width: 8.w),
                Flexible(
                  child: Text(
                    'Web Results',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  '${controller.webResults.length} results',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.green.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16.w),
              itemCount: controller.webResults.length,
              itemBuilder: (context, index) {
                final result = controller.webResults[index];
                return _buildWebCard(result, controller);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWebCard(WebSearchResult result, ProblemSolverController controller) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Card(
      elevation: 2,
      margin: EdgeInsets.only(bottom: 12.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: () async {
          try {
            await controller.launchURL(result.link);
            _showSnackbar('Success', 'Opening web link', ContentType.success);
          } catch (e) {
            _showSnackbar('Error', 'Failed to open web link: $e', ContentType.failure);
          }
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                result.title,
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                  fontSize: 16.sp,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 8.h),
              Text(
                result.snippet,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 14.sp,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 8.h),
              Row(
                children: [
                  Icon(Icons.link, size: 16.sp, color: Colors.green),
                  SizedBox(width: 4.w),
                  Expanded(
                    child: Text(
                      result.link,
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 80.sp, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            message,
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedInputPanel(ProblemSolverController controller) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return SingleChildScrollView(
      child: Container(
        margin: EdgeInsets.all(16.w),
        padding: EdgeInsets.all(isTablet ? 24.w : 20.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 20.r,
              offset: Offset(0, -4.h),
            ),
          ],
        ),
        child: Column(
          children: [
            if (controller.imageFile != null || controller.audioPath != null)
              _buildEnhancedAttachmentsPreview(controller),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFF8FAFC),
                      borderRadius: BorderRadius.circular(24.r),
                      border: Border.all(color: const Color(0xFFE5E7EB)),
                    ),
                    child: TextField(
                      controller: _textController,
                      onChanged: controller.setDoubtText,
                      decoration: InputDecoration(
                        hintText: 'Ask me anything...',
                        hintStyle: TextStyle(
                          color: const Color(0xFF9CA3AF),
                          fontWeight: FontWeight.w400,
                          fontSize: 16.sp,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                      ),
                      maxLines: 3,
                      minLines: 1,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF374151),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                _buildModernAttachmentButton(
                  Icons.image,
                  const Color(0xFF3B82F6),
                  () => _showImageChoiceDialog(context, controller),
                ),
                SizedBox(width: 8.w),
                _buildModernAttachmentButton(
                  controller.isRecording ? Icons.stop : Icons.mic,
                  controller.isRecording ? const Color(0xFFEF4444) : const Color(0xFFF59E0B),
                  controller.toggleAudioRecording,
                ),
                SizedBox(width: 8.w),
                _buildModernAttachmentButton(
                  Icons.send,
                  const Color(0xFF10B981),
                  () => _handleDoubtSolver(controller),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernAttachmentButton(IconData icon, Color color, VoidCallback onPressed) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: color, size: 22.sp),
        style: IconButton.styleFrom(
          padding: EdgeInsets.all(12.w),
          minimumSize: Size(48.w, 48.h),
        ),
      ),
    );
  }

  Widget _buildEnhancedAttachmentsPreview(ProblemSolverController controller) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            if (controller.imageFile != null)
              _buildEnhancedImagePreview(controller),
            if (controller.audioPath != null)
              _buildEnhancedAudioPreview(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedImagePreview(ProblemSolverController controller) {
    final isTablet = ScreenUtil().screenWidth >= 600;
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                spreadRadius: 0,
                blurRadius: 8.r,
                offset: Offset(0, 2.h),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Image.file(
              controller.imageFile!,
              height: isTablet ? 100.h : 80.h,
              width: isTablet ? 120.w : 100.w,
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: -4,
          right: -4,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFFEF4444),
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFEF4444).withOpacity(0.3),
                  spreadRadius: 0,
                  blurRadius: 4.r,
                  offset: Offset(0, 2.h),
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(Icons.close, color: Colors.white, size: 16.sp),
              onPressed: () {
                controller.clearAttachments();
                _showSnackbar('Success', 'Image attachment removed', ContentType.success);
              },
              style: IconButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size(24.w, 24.h),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedAudioPreview(ProblemSolverController controller) {
    return Stack(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          decoration: BoxDecoration(
            color: const Color(0xFFF59E0B).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: const Color(0xFFF59E0B).withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.audiotrack, color: const Color(0xFFF59E0B), size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                'Audio',
                style: TextStyle(
                  color: const Color(0xFFF59E0B),
                  fontWeight: FontWeight.w500,
                  fontSize: 14.sp,
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: -4,
          right: -4,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFFEF4444),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: IconButton(
              icon: Icon(Icons.close, color: Colors.white, size: 16.sp),
              onPressed: () {
                controller.clearAttachments();
                _showSnackbar('Success', 'Audio attachment removed', ContentType.success);
              },
              style: IconButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size(24.w, 24.h),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleDoubtSolver(ProblemSolverController controller) async {
    if (controller.doubtText.isEmpty && controller.imageFile == null && controller.audioPath == null) {
      _showSnackbar('Warning', 'Please enter a question or attach media', ContentType.warning);
      return;
    }
    await controller.performSearch(SearchType.doubtSolver);
    _textController.clear();
    _showSnackbar('Success', 'Question submitted to AI Tutor', ContentType.success);
  }

  Future<void> _handleSearch(ProblemSolverController controller, SearchType searchType) async {
    if (controller.doubtText.isEmpty) {
      _showSnackbar('Warning', 'Please enter a search query', ContentType.warning);
      return;
    }
    await controller.performSearch(searchType);
    _textController.clear();
    _showSnackbar('Success', searchType == SearchType.youtubeSearch ? 'YouTube search completed' : 'Web search completed', ContentType.success);
  }

  void _showImageChoiceDialog(BuildContext context, ProblemSolverController controller) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: double.infinity,
                  height: 4.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),
                SizedBox(height: 20.h),
                ListTile(
                  leading: Icon(Icons.photo_library, color: Colors.blue, size: 24.sp),
                  title: Text('From Gallery', style: TextStyle(fontSize: 16.sp)),
                  onTap: () async {
                    await controller.pickImageFromGallery();
                    Navigator.pop(context);
                    if (controller.imageFile != null) {
                      _showSnackbar('Success', 'Image selected from gallery', ContentType.success);
                    } else {
                      _showSnackbar('Error', 'Failed to select image', ContentType.failure);
                    }
                  },
                ),
                ListTile(
                  leading: Icon(Icons.camera_alt, color: Colors.green, size: 24.sp),
                  title: Text('Take Photo', style: TextStyle(fontSize: 16.sp)),
                  onTap: () async {
                    await controller.captureImageWithCamera();
                    Navigator.pop(context);
                    if (controller.imageFile != null) {
                      _showSnackbar('Success', 'Photo captured', ContentType.success);
                    } else {
                      _showSnackbar('Error', 'Failed to capture photo', ContentType.failure);
                    }
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}